'use client';
import type { VariantProps } from 'class-variance-authority';
import type { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { signInSchema } from '@/features/auth/services/auth.validation';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import { Button } from '@/shared/components/ui/button';
import { EyeCloseIcon, EyeIcon } from '@/shared/icons';
import Alert from '../ui/alert/Alert';
import {
  signInFormContainerVariants,
  signInFormDescriptionVariants,
  signInFormIconVariants,

  signInFormTitleVariants,
} from './sign-in-form-variants';
import { useTranslations } from 'next-intl';
import { toast } from 'sonner';

type SignInFormProps = {
  variant?: VariantProps<typeof signInFormContainerVariants>['variant'];
};

/**
 * SignInForm Component
 *
 * This component uses semantic color tokens from our theming system.
 * It provides a form for users to sign in to the application.
 */
export default function SignInForm({ variant = 'default' }: SignInFormProps = {}) {
  // const [isChecked, setIsChecked] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const t = useTranslations('signIn');

  // Use our new auth hook
  const { login, isLoading, error } = useAuth();

  const form = useForm<z.infer<typeof signInSchema>>({
    resolver: zodResolver(signInSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const {
    handleSubmit,
    formState: { errors, isSubmitting },
  } = form;

  async function onSubmit(data: z.infer<typeof signInSchema>) {
    try {
      // Use the login method from our auth hook
      await login({
        email: data.email,
        password: data.password,
      });

      toast.success(t('loginSuccess'));
      // The hook handles redirects, token storage, and error handling
    } catch (err) {
      // Error is already handled by the hook
      console.log('Login failed', err);
    }
  }

  // Use error from auth hook
  const displayError = error?.message || null;

  return (
    <div className={signInFormContainerVariants({ variant })}>
      <div className="w-full max-w-md sm:pt-10 mx-auto mb-5">
        {/* <Link
          href="/"
          className={signInFormBackLinkVariants({ variant })}
        >
          <ChevronLeftIcon className="size-5" />
          Back to Home
        </Link> */}
      </div>
      <div className="flex flex-col justify-center flex-1 w-full max-w-md mx-auto">
        <div>
          <div className="mb-5 sm:mb-8">
            <h1 className={signInFormTitleVariants({ variant })}>
              {t('label')}
            </h1>
            <p className={signInFormDescriptionVariants({ variant })}>
              {t('description')}
            </p>
          </div>

          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="space-y-6">
              <div>
                <Label>
                  {t('email')}
                  {' '}
                  <span className="text-error-500">*</span>
                  {' '}
                </Label>
                <Controller
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder="<EMAIL>"
                      type="email"
                      error={!!errors.email?.message}
                      hint={errors.email?.message}
                    />
                  )}
                />
              </div>
              <div>
                <Label>
                  {t('password')}
                  {' '}
                  <span className="text-error-500">*</span>
                  {' '}
                </Label>
                <div className="relative">
                  <Controller
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <Input
                        {...field}
                        placeholder={t('placeholder')}
                        type={showPassword ? 'text' : 'password'}
                        error={!!errors.password?.message}
                        hint={errors.password?.message}
                      />
                    )}
                  />
                  <span
                    tabIndex={0}
                    role="button"
                    onClick={() => setShowPassword(!showPassword)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        setShowPassword(!showPassword);
                      }
                    }}
                    className="absolute z-30 -translate-y-1/2 cursor-pointer right-4 top-1/2"
                  >
                    {showPassword
                      ? (
                          <EyeIcon className={signInFormIconVariants({ variant })} />
                        )
                      : (
                          <EyeCloseIcon className={signInFormIconVariants({ variant })} />
                        )}
                  </span>
                </div>
              </div>

              {/* <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Checkbox checked={isChecked} onChange={setIsChecked} />
                    <span className="block font-normal text-gray-700 text-theme-sm dark:text-gray-400">
                      Keep me logged in
                    </span>
                  </div>
                  <Link
                    href="/reset-password"
                    className="text-sm text-brand-500 hover:text-brand-600 dark:text-brand-400"
                  >
                    Forgot password?
                  </Link>
                </div> */}

              {displayError && <Alert variant="error" title="Error" message={displayError} />}

              <div>
                <Button className="w-full" type="submit" disabled={isSubmitting || isLoading}>
                  {isLoading ? t('logging') : t('login')}
                </Button>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
