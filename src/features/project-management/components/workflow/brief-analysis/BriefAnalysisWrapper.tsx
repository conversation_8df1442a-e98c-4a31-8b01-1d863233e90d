'use client';

import { useEffect, useMemo, useRef, useState } from 'react';
import { useCoAgent, useCopilotAction } from '@copilotkit/react-core';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useCurrentStep, useCurrentTask, useProjectInfo, useProjectName, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import type { EditorContentChanged, stateRouteAgent } from '@/shared/types/global';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { EEndpointApiCopilotkit, ENameStateAgentCopilotkit, ETaskNameCopilot } from '@/shared/enums/global';
import BaseBriefAnalysis from './BaseBriefAnalysis';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { useGetListTemplates } from '@/features/project-management/hooks/useProjectTemplate';
import type { ProjectCampaignEnumVer2, TemplateFiles } from '@/features/project-management/types/project';
import { ETypeFile } from '@/features/project-management/types/project';
import { useParams } from 'next/navigation';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { htmlToMarkdownVer2, markdownToHTMLToDocFile, markdownToHtmlVer2 } from '@/shared/components/ui/editor/parser';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { downloadMDToFile } from '@/shared/utils/convertMDtoDocFile';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import { useLocale, useTranslations } from 'next-intl';
import { useChatBoxMessage } from '@/features/project-management/stores/chatbox-store';

type BriefAnalysisResponse = {
  infos: { value: string }[];
  model: string;
};

const BriefAnalysisWrapper: React.FC = () => {
  const t = useTranslations('workflow');

  const [isShowEditButton, _setIsShowEditButton] = useState(false);

  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [templateFile, setTemplateFile] = useState<TemplateFiles[]>([]);

  const [campaignSelected, setCampaignSelected] = useState<ProjectCampaignEnumVer2 | null>(null);

  const [isSaved, _setIsSaved] = useState(true);

  const [isClickUnSaved, setIsClickUnSaved] = useState(false);

  const [isShowModal, setIsShowModal] = useState(false);

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  const titleConfirm = t('common.titleConfirmChange');

  const titleUnSave = t('common.titleUnSave');

  const descriptionUnSave = t('common.descriptionUnSave');

  const descriptionConfirm = t('common.descriptionConfirm');

  const [titlePopup, setTitlePopUp] = useState<string>(titleConfirm);

  const [descriptionPopUp, setDescriptionPopUp] = useState<string>(descriptionConfirm);

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync } = useUpdateStatusStep();

  const { registerStep, clearStep } = useDirty();

  // const { appendMessage } = useCopilotChat();

  const { data: templates } = useGetListTemplates();

  const project = useProjectInfo();

  const params = useParams<{ id: string }>();

  const locale = useLocale();

  const abortControllerRef = useRef<AbortController | null>(null);

  const projectName = useProjectName();

  const lastMessage = useChatBoxMessage();

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: t('common.titleUnSave'),
    message: t('common.descriptionGuard'),
  });

  useEffect(() => {
    if (project) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setCampaignSelected(project.campaign);
    }
  }, [project]);

  useEffect(() => {
    if (templates && campaignSelected?.toString()) {
      const templateSelect = templates.filter(template => template.campaign === campaignSelected);
      let urlOptions: TemplateFiles[] = [];
      templateSelect.forEach(template => urlOptions = [...urlOptions, ...template.files]);

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setTemplateFile(urlOptions);
    }
  }, [templates, campaignSelected]);

  const {
    completeStep,
    getNextStepId,
    updateStatus,
  } = useWorkflowActions();
  const currentStep = useCurrentStep();
  const currentTask = useCurrentTask();
  const currentStepId = currentStep?.id;

  const { data: briefAnalysis } = useGetInfoDetail<BriefAnalysisResponse, any>(currentStep?.id ?? '');

  const { state } = useCoAgent<stateRouteAgent<BriefAnalysisFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
      agent_name: AGENT_NAME_COPILOTKIT.ANALYSIS,
    },
  });

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setForm(data);
      setIsLoading(false);
    };
    updateState();
  };

  // const updateStatusShowEditButton = (status: boolean) => {
  //   setIsShowEditButton(status);
  // };

  const saveDataFromAI = async (markdown: string, status: EStatusTask = EStatusTask.IN_PROGRESS) => {
    // convert same format data
    const html = await markdownToHtmlVer2(markdown);
    const markDownConvert = htmlToMarkdownVer2(html);

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markDownConvert }],
          model: modelAISelected,
        },
      ],
    };

    if (currentStepId) {
      await updateQuestionAnswer(payload, currentStepId);
      mutateAsync({ id: currentStepId, status });
    }
  };

  const getAnalysisBriefData = async (data: any) => {
    setIsLoading(true);
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.ANSWER }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const brief = res.data.result;
      updateMarkdownToState(brief);
      saveDataFromAI(brief);
    } catch (error: any) {
      console.error(error);
    }
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (briefAnalysis && briefAnalysis?.stepInfo.length) {
      const markdown = briefAnalysis.stepInfo[0]?.infos[0]?.value;

      updateMarkdownToState(markdown ?? '');
      // if (currentStep?.status !== EStatusTask.COMPLETED) {
      //   updateStatusShowEditButton(false);
      // } else {
      //   updateStatusShowEditButton(true);
      // }
    } else {
      if (briefAnalysis?.stepInfoPrevious.length) {
        const briefFile = briefAnalysis?.stepInfoPrevious[0].infos[0];
        const files = briefFile.files;
        if (!templateFile.length) {
          return;
        }
        const data = {
          project_id: params.id,
          llm: modelAISelected,
          client_brief_url: getFile(files, true),
          ...templateFile.reduce((result, template) => {
            if (template.type === ETypeFile.BRIEF_TEMPLATE) {
              result.template_brief_url = [
                ...(result.template_brief_url || []),
                ...getFile([template.file], true),
              ];
            }
            if (template.type === ETypeFile.BRIEF_QUESTION) {
              result.question_brief_url = [
                ...(result.question_brief_url || []),
                ...getFile([template.file], true),
              ];
            }
            return result;
          }, {} as any),
        };
        getAnalysisBriefData(data);
      }
    }

    if (briefAnalysis?.stepInfoPrevious.length) {
      const model = briefAnalysis?.stepInfoPrevious[0].model;
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAIDefault(model);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAISelected(model);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [briefAnalysis, templateFile]);

  useEffect(() => {
    const briefAnalysisState = state[ENameStateAgentCopilotkit.ANALYSIS];
    if (briefAnalysisState && briefAnalysisState.answer_brief && briefAnalysisState.answer_brief_process && briefAnalysisState.answer_brief_process === 'done') {
      updateMarkdownToState(briefAnalysisState.answer_brief);
    }
  }, [state]);

  const compareMarkdown = (form?: string) => {
    const markdownInitial = briefAnalysis?.stepInfo[0]?.infos[0]?.value ?? '';
    const markdownCurrent = markdown;

    return markdownInitial === (form || markdownCurrent);
  };

  const toggleEditMode = () => {
    setIsEditMode(prev => !prev);
  };

  const handleUpdateBriefAnalysis = async () => {
    const projectId = project?.id ?? '';

    const data = {
      project_id: projectId,
      llm: modelAISelected,
      task_name: ETaskNameCopilot.ANSWER_QUESTION_BRIEF,
      instructions: lastMessage,
      original_content: markdown,
    };

    const baseUrl = window.location.origin;
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
      method: 'POST',
      body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.EDIT_CONTENT }),
      signal: abortControllerRef.current.signal,
    });

    const res = await response.json();

    const { result } = res.data;

    const { explain } = result;

    const { output } = result;

    saveDataFromAI(output, EStatusTask.COMPLETED);
    return explain;
  };

  useCopilotAction({
    name: 'updateBriefAnalysis',
    description: 'update answer and question of brief analysis',
    parameters: [],
    handler: async () => {
      return await handleUpdateBriefAnalysis();
    },
    render: ({ result }) => {
      return (
        <div className="relative py-2 px-4 rounded-2xl rounded-tl-sm max-w-[80%] text-sm leading-relaxed bg-white border border-neutral-200 shadow-sm text-neutral-600">
          {
            result
            || (
              <div className="flex items-center gap-2 p-1">
                <div className="w-2 h-2 bg-pink-600 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
                <div className="w-2 h-2 bg-pink-600 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
                <div className="w-2 h-2 bg-pink-600 rounded-full animate-bounce"></div>
              </div>
            )
          }
        </div>
      );
    },
  });

  // const sendBriefAnswer = () => {
  //   setState(prev => ({
  //     ...prev,
  //     agent_name: AGENT_NAME_COPILOTKIT.ANALYSIS,
  //     [ENameStateAgentCopilotkit.ANALYSIS]: {
  //       answer_brief: markdown,
  //       ...templateFile.reduce((result, template) => {
  //         if (template.type === ETypeFile.BRIEF_TEMPLATE) {
  //           result.template_brief_url = [
  //             ...(result.template_brief_url || []),
  //             ...getFile([template.file]),
  //           ];
  //         }
  //         if (template.type === ETypeFile.BRIEF_QUESTION) {
  //           result.question_brief_url = [
  //             ...(result.question_brief_url || []),
  //             ...getFile([template.file]),
  //           ];
  //         }
  //         return result;
  //       }, {} as any),
  //     },
  //   }));

  //   appendMessage(
  //     new TextMessage({
  //       content: MESSAGE_SEND_ROUTE_AGENT,
  //       role: Role.Developer,
  //     }),
  //   );
  // };

  const handleFinishStep = async (form?: string) => {
    if (!currentStepId) {
      return;
    }

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: form ?? markdown }],
          model: modelAISelected,
        },
      ],
    };

    await mutateAsync({
      id: currentStepId,
      status: EStatusTask.COMPLETED,
      select: 'all',
      isGenerate: true,
      stepIds: [],
      stepInfoIds: [],
    });

    await updateQuestionAnswer(payload, currentStepId);

    _setIsSaved(true);

    clearStep(currentStepId);
  };

  const handleApprove = async () => {
    if (!currentStepId) {
      return;
    }

    setIsEditMode(false);

    const nextStepId = getNextStepId();

    const nextStep = currentTask?.children.find(t => t.id === nextStepId);

    if (nextStep) {
      const status = nextStep.status;
      if (status === EStatusTask.COMPLETED && currentStep.status !== EStatusTask.COMPLETED) {
        updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
        await mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
      }
    }

    if (currentStep.status !== EStatusTask.COMPLETED) {
      await mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
      await handleFinishStep();
      // sendBriefAnswer();
    }

    completeStep(currentStepId);
  };

  const handleEditorChange = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);

    if (!currentStepId) {
      return;
    }
    const isChanged = compareMarkdown(markdown);

    const isChangedModel = modelAIDefault !== modelAISelected;

    _setIsSaved(isChanged || !isChanged);
    registerStep(currentStepId!, () => !isChanged || isChangedModel);
  };

  const discardChange = () => {
    if (!currentStep) {
      return;
    }

    const isChanged = compareMarkdown(form);
    if (!isChanged && currentStep.status === EStatusTask.COMPLETED) {
      setTitlePopUp(titleUnSave);
      setDescriptionPopUp(descriptionUnSave);
      setIsClickUnSaved(true);
      setIsShowModal(true);
      return;
    }

    clearStep(currentStepId ?? '');
    setForm(markdown);
    setIsEditMode(false);
  };

  const confirmChange = () => {
    if (!currentStep) {
      return;
    }

    const isChanged = compareMarkdown(form);
    if (!isChanged && currentStep.status === EStatusTask.COMPLETED) {
      setIsClickUnSaved(false);
      setTitlePopUp(titleConfirm);
      setDescriptionPopUp(descriptionConfirm);
      setIsShowModal(true);
      return;
    }

    clearStep(currentStepId ?? '');
    setMarkdown(form);
    setIsEditMode(false);
  };

  const handleConfirmPopUp = () => {
    if (isClickUnSaved) {
      setForm(markdown);
      setIsEditMode(false);
      setIsShowModal(false);
      return;
    }

    clearStep(currentStepId ?? '');
    handleFinishStep(form);
    setMarkdown(form);
    setIsEditMode(false);
    setIsShowModal(false);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleDownloadFile = async () => {
    if (!currentStep) {
      return;
    }
    const nameStep = currentStep.name;
    const html = await markdownToHTMLToDocFile(markdown);
    await downloadMDToFile(html, projectName, nameStep[locale]!);
  };

  const handleReGenBrief = async () => {
    if (!currentStepId) {
      return;
    }
    updateStatus(currentStepId, EStatusTask.IN_PROGRESS);
    updateStatus(currentTask?.id ?? '', EStatusTask.IN_PROGRESS, true);

    const previousStep = briefAnalysis?.stepInfoPrevious[0].stepId ?? '';

    const payloadPreviousStep = briefAnalysis?.stepInfoPrevious[0].infos;

    await updateQuestionAnswer({ stepInfos: [{
      infos: [...payloadPreviousStep],
      order: 0,
      model: modelAISelected,
    }] }, previousStep);

    await updateQuestionAnswer({ stepInfos: [] }, currentStepId);
    setIsLoading(true);
    setMarkdown('');
  };

  const isChangedModel = useMemo(() => {
    return modelAIDefault !== modelAISelected;
  }, [modelAISelected, modelAIDefault]);

  const handleChangeModel = (data: string) => {
    setModelAISelected(data);

    const isChanged = compareMarkdown(markdown);

    const isChangedModel = modelAIDefault !== data;

    _setIsSaved(isChanged || !isChanged);
    registerStep(currentStepId!, () => !isChanged || isChangedModel);
  };

  return (
    <>
      <BaseBriefAnalysis
        isLoading={isLoading}
        markdown={markdown}
        form={form}
        isEditMode={isEditMode}
        isShowEditButton={isShowEditButton}
        onEditToggle={toggleEditMode}
        onConfirmChange={confirmChange}
        onDiscardChange={discardChange}
        onEditorChange={handleEditorChange}
        onApprove={handleApprove}
        onDownloadFile={handleDownloadFile}
        modelAIDefault={modelAIDefault}
        onChangeModel={handleChangeModel}
        isShowButtonReGenDropdown={isChangedModel}
        onReGen={handleReGenBrief}
      />

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => {}}
        title={titlePopup}
        description={descriptionPopUp}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText={t('common.continue')}
        cancelText={t('common.cancel')}
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </>
  );
};

export default BriefAnalysisWrapper;
