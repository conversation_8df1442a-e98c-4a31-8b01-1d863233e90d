'use client';
import { But<PERSON> } from '@/shared/components/ui/button';
import { ArrowDownTrayIcon, BoxCubeIcon } from '@/shared/icons';
import QuestionnaireAnalysis from '../discovery-questionnaire/QuestionnaireAnalysis';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'next/navigation';
import { useCurrentStep, useProjectName, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { documentFileUpload, ScoringReportDataType } from '@/features/project-management/types/project';
import type { IFileResponse } from '@/shared/types/global';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import { EReportType } from '@/features/project-management/types/report';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { EStatusTask } from '@/features/project-management/types/workflow';
import ScoringOverview from '../5_client_assessment/ScoringOverview';
import { useModal } from '@/shared/hooks/useModal';
import { Modal } from '@/shared/components/ui/modal';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { RefreshCcw } from 'lucide-react';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { useQueryClient } from '@tanstack/react-query';
import { downloadMDToFile } from '@/shared/utils/convertMDtoDocFile';
import { markdownToHTMLToDocFile } from '@/shared/components/ui/editor/parser';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import SelectModelAI from '../common/SelectModelAI';
import { useLocale, useTranslations } from 'next-intl';
import MessageWarning from '../common/MessageWarning';
import { useChatBoxVisible } from '@/features/project-management/stores/chatbox-store';

type ReportType = {
  title: string;
  header: string;
  description: string;
  stepId: string;
  id: string;
  data: any[];
  dataReport: any[];
  templates: IFileResponse[];
  evaluationFramework: string;
  nameForm: string;
  type: number;
  onBackDashboard: () => void;
};

const Report: React.FC<ReportType> = ({
  title,
  header,
  description,
  data,
  dataReport,
  templates,
  id,
  stepId,
  type,
  nameForm,
  evaluationFramework,
  onBackDashboard,
}) => {
  const t = useTranslations('workflow');

  const locale = useLocale();

  const isVisible = useChatBoxVisible();

  const { isOpen, openModal, closeModal } = useModal();

  const [markdown, setMarkdown] = useState<string>('');

  const [analysisBrief, setAnalysisBrief] = useState<string>('');

  const [isFirstCall, setIsFirstCall] = useState<boolean>(false);

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [status, _setStatus] = useState<boolean>(false);

  const [scoringData, setScoringData] = useState<ScoringReportDataType | null>(null);

  const [isShowReGen, setIsShowReGen] = useState<boolean>(false);

  const [idsInForm, setIdsInForm] = useState<string[]>([]);

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  const params = useParams<{ id: string }>();

  const queryClient = useQueryClient();

  const workflow = useWorkflowTasks();

  const { mutateAsync: refreshData } = useUpdateStatusStep();

  const idSOW = workflow[1]?.steps[2]?.id;

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const currentStep = useCurrentStep();

  const projectName = useProjectName();

  const { data: dataSOW } = useGetInfoDetail<any, documentFileUpload>(idSOW ?? '');

  const abortControllerRef = useRef<AbortController | null>(null);

  const saveDataFromAI = async (markdown: string, status: EStatusTask) => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: type === EReportType.REPORT ? 0 : 1,
          type,
          model: modelAISelected,
          infos: [
            { form: markdown, status },
          ],
        },

      ],
    };

    await updateQuestionAnswer(
      payload,
      stepId,
    );
  };

  useEffect(() => {
    if (dataSOW && dataSOW.stepInfo.length) {
      const brief = dataSOW?.stepInfo[0]?.infos[0]?.value ?? '';

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setAnalysisBrief(brief);
    }
  }, [dataSOW]);

  const getScoringContent = async (data: string) => {
    const payload = {
      project_id: params.id,
      evaluation_framework: evaluationFramework,
      content_to_score: data,
      llm: modelAISelected,
    };
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.SCORING_CONTENT }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const evaluationReport = res.data.result.evaluate_report;
      const evaluationScore = res.data.result.score;

      const data = { report: evaluationReport, score: evaluationScore };
      const payloadScoring = {
        formStepId: id,
        stepInfos: [
          {
            type: EReportType.SCORING,
            order: 2,
            infos: [{ value: data }],
            model: modelAISelected,
          },
        ],
      };

      await updateQuestionAnswer(payloadScoring, stepId);

      setScoringData(data);
    } catch (error: any) {
      console.log(error);
    }
  };

  const getSummaryReport = async () => {
    if (isFirstCall) {
      return;
    }

    if (!analysisBrief) {
      return;
    }
    const report: string[] = dataReport.flatMap(step =>
      step.infos.map((info: any) => info.form ? info.form : info.value),
    ).filter(form => form != null);

    if (!report.length) {
      return;
    }
    setIsFirstCall(true);
    const payload = {
      project_id: params.id,
      brief_analysis: analysisBrief,
      multiple_report: report,
      report_template_url: [...getFile(templates.filter(t => t.category === 'report'), true)],
      llm: modelAISelected,
    };

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.SUMMARY_REPORT }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();

      const markdown = res.data.result;
      await getScoringContent(markdown);
      setMarkdown(markdown);
      setIsLoading(false);
      saveDataFromAI(markdown, EStatusTask.IN_PROGRESS);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (!data.length) {
      getSummaryReport();
    } else {
      const dataReport = data.find(d => d.type === EReportType.REPORT);

      if (dataReport) {
        const data = dataReport.infos[0].form;

        const model = dataReport.model;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setModelAIDefault(model);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setModelAISelected(model);
        // const status = dataReport.infos[0].status;

        // setStatus(status === EStatusTask.COMPLETED);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setMarkdown(data);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsLoading(false);
      }

      const scoringData = data.find(d => d.type === EReportType.SCORING);
      if (scoringData) {
        const data = scoringData.infos[0].value;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setScoringData(data);
      }

      const isGenInform = data.some(d => d.isGenerate);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIdsInForm(data.map(d => d.id));
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsShowReGen(isGenInform);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, analysisBrief]);

  const isChangeModel = useMemo(() => {
    return modelAIDefault !== modelAISelected;
  }, [modelAIDefault, modelAISelected]);

  const onClickApprove = (markdown: string) => {
    saveDataFromAI(markdown, EStatusTask.COMPLETED);
  };

  const handleReGen = async () => {
    setIsLoading(true);
    // setStatus(false);
    setMarkdown('');
    setScoringData(null);
    setIsShowReGen(false);
    setIsFirstCall(false);

    await refreshData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: [...idsInForm],
      select: 'all',
    });

    queryClient.invalidateQueries({ queryKey: ['getInfoDetail', stepId], type: 'all' });
  };

  const handleDownloadFile = async () => {
    if (!currentStep) {
      return;
    }
    const nameStep = currentStep?.name;
    const html = await markdownToHTMLToDocFile(markdown);

    await downloadMDToFile(html, projectName, nameStep[locale]!, nameForm);
  };

  const getTypeView = useMemo(() => {
    return type === EReportType.REPORT ? t('report.reportType') : t('report.presentType');
  }, [type]);

  const handleChangeModelAI = (data: string) => {
    setModelAISelected(data);
  };

  return (
    <>
      <div className="sticky top-0 z-100 bg-white flex items-center justify-between p-4 md:p-6 pb-2! border-b border-gray-200">
        <h6 className="pb-0!">
          {title}
        </h6>

        <ul>
          <li className="pb-0! text-sm text-black list-disc">{getTypeView}</li>
        </ul>

      </div>

      <div className="sticky top-13 z-100 pb-3 bg-white flex items-center justify-between gap-3 p-4 md:p-6">
        <div className="flex flex-col gap-2">
          <h2 className="text-xl font-medium mb-0 text-green-600">
            {header}
          </h2>

          <p className="mb-0">{description}</p>

        </div>

        <div className="flex items-center gap-2">
          {scoringData
            && <ScoringOverview score={scoringData?.score ?? 0} openModal={openModal} />}

          <Button
            variant="secondary"
            onClick={onBackDashboard}
          >
            <BoxCubeIcon className="h-5 w-5" />
            {t('common.backToDashboard')}
          </Button>

          { !!markdown && (
            <Button
              type="button"
              variant="outline"
              onClick={handleDownloadFile}
            >
              <ArrowDownTrayIcon className="h-5 w-5 " />
            </Button>
          )}

          {
            ((isShowReGen || isChangeModel) && !isLoading)
            && (
              <Button onClick={handleReGen} type="button" variant="outline" className="text-cyan-500 bg-cyan-50">
                <RefreshCcw className="h-5 w-5 " />
              </Button>
            )
          }

          {(!isLoading && !isVisible) && (
            <SelectModelAI
              onChangeModel={handleChangeModelAI}
              defaultValue={modelAIDefault}
              disable={false}
              position="unset"

            />
          )}

        </div>
      </div>

      {isLoading
        ? (
            <div className="p-4 md:p-6">
              <ProjectCardSkeleton />
              <MessageWarning />
            </div>
          )
        : (
            <div className="relative p-4 md:p-6 pt-0!">
              <QuestionnaireAnalysis
                stepId={stepId}
                data={markdown}
                isHiddenApproveButton={status}
                isHiddenBackButton={true}
                isFinish={status}
                onBack={() => {}}
                onSubmit={onClickApprove}
              />
            </div>
          )}
      <Modal
        showCloseButton={false}
        isOpen={isOpen}
        onClose={closeModal}
        className="overflow-auto
      w-[980px] max-h-[700px]"
      >
        <div className="no-scrollbar w-full p-6 h-full">
          <div className="overflow-auto h-full"><MarkdownRenderer content={scoringData?.report ?? ''} /></div>
        </div>
      </Modal>
    </>

  );
};

type ResearchDetailType = {
  id: string;
  type: EReportType | null;
  stepFormId: string;
  nameTemplate: string;
  onBackDashboard: () => void;
};
const ReportDetailView: React.FC<ResearchDetailType> = ({ id, stepFormId, type, nameTemplate, onBackDashboard }) => {
  const t = useTranslations('workflow');

  const [dataFormStep, setDataFormStep] = useState<any | null>(null);

  const [data, setData] = useState<any[]>([]);

  const [nameForm, setNameForm] = useState<string>('');

  const { data: dataStep } = useGetInfoDetail<any, any>(id);

  const [evaluationFramework, setEvaluationFramework] = useState<string>('');

  const [dataReport, setDataReport] = useState<any[]>([]);

  useEffect(() => {
    if (type === null) {
      return;
    }
    if (dataStep && dataStep.formStep.length) {
      const data = dataStep.formStep.find(t => t.infos[0]?.type === type && t.id === stepFormId);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setNameForm(data?.name);

      if (data) {
        const dataStepInfo = dataStep.stepInfo.filter((step: any) => step.formStepId === data.id);

        const ids: string[] = data.infos[0].input;
        const idSet = new Set(ids.map(item => item));

        const groupedByFormStepId = new Map<string, any>();

        for (const step of dataStep.stepInfoPrevious) {
          const formStepId = step.formStepId;

          if (!idSet.has(formStepId)) {
            continue;
          }

          const existing = groupedByFormStepId.get(formStepId);

          if (!existing && !!step?.infos?.[0]?.isFinish) {
            groupedByFormStepId.set(formStepId, step);
          }
        }
        const dataReport = Array.from(groupedByFormStepId.values());

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setData(dataStepInfo);

        const raw = data?.infos?.[0]?.raw;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setEvaluationFramework(raw ?? '');

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setDataReport(dataReport);
      }

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setDataFormStep(data ?? []);
    }
  }, [dataStep, type, stepFormId]);

  if (!dataFormStep) {
    return (
      <div className="p-4 md:p-6">
        <ProjectCardSkeleton />
      </div>
    );
  }

  return (

    <Report
      title={nameTemplate}
      header={t('report.header')}
      description={t('report.description')}
      stepId={id}
      id={dataFormStep.id}
      data={data}
      evaluationFramework={evaluationFramework}
      nameForm={nameForm}
      dataReport={dataReport}
      templates={dataFormStep.infos?.[0]?.template ?? []}
      type={type as number}
      onBackDashboard={onBackDashboard}
    />
  );
};

export default ReportDetailView;
