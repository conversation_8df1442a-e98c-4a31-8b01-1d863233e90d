import type { MessageType } from '../types/chat';
import { ActionExecutionMessage, ResultMessage, TextMessage } from '@copilotkit/runtime-client-gql';

export const ConvertMessageFromConversation = (messages: MessageType[]) => {
  return messages.map((msg) => {
    if (msg.type === 'ActionExecutionMessage') {
      return new ActionExecutionMessage({
        id: msg.id,
        name: msg.name,
        scope: msg.scope,
        arguments: msg.arguments,
        createdAt: msg.createdAt,
      });
    } else if (msg.type === 'ResultMessage') {
      return new ResultMessage({
        id: msg.id,
        actionExecutionId: msg.actionExecutionId,
        actionName: msg.actionName,
        result: msg.result,
        createdAt: msg.createdAt,
      });
    } else {
      return new TextMessage({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        createdAt: msg.createdAt,
      });
    }
  });
};
