import type { CampaignListType } from '../types/project';
import { ProjectCampaignEnumVer2, ProjectTypeVer2Enum } from '../types/project';

const BlueCCampaignList: CampaignListType[] = [
  {
    label: '<PERSON><PERSON><PERSON> chuyện văn hóa',
    value: ProjectCampaignEnumVer2.CCVH,
  },
  {
    label: 'Employer Branding',
    value: ProjectCampaignEnumVer2.EMPLOYER_BRANDING,
  },
  {
    label: 'Gắn kết nhân viên',
    value: ProjectCampaignEnumVer2.GKNV,
  },
  {
    label: 'Hiệu Suất Cao',
    value: ProjectCampaignEnumVer2.HSC,
  },
  {
    label: 'Sổ tay VHDN',
    value: ProjectCampaignEnumVer2.ST_VHDN,
  },
  {
    label: 'Trải nghiệm nhân viên',
    value: ProjectCampaignEnumVer2.TNNV,
  },
  {
    label: 'T<PERSON>y<PERSON><PERSON> thông nội bộ',
    value: ProjectCampaignEnumVer2.TTNB,
  },
  {
    label: 'Văn hóa doanh nghiệp',
    value: ProjectCampaignEnumVer2.VHDN,
  },
  {
    label: 'Văn hóa lấy khách hàng làm trung tâm',
    value: ProjectCampaignEnumVer2.VHLKHLTT,
  },
  {
    label: 'Văn hóa số',
    value: ProjectCampaignEnumVer2.VHS,
  },
  {
    label: 'Truyền thông Nền tảng văn hóa doanh nghiệp',
    value: ProjectCampaignEnumVer2.TTNT_VHDN,
  },
];

const MiBrandCampaignList: CampaignListType[] = [
  {
    label: 'Market Research',
    value: ProjectCampaignEnumVer2.MARKET_RESEARCH,
  },
  {
    label: 'Branding',
    value: ProjectCampaignEnumVer2.BRANDING,
  },
];

const MVVAcademyCampaignList: CampaignListType[] = [];

const SNPCampaignList = [
  // { value: ProjectCampaignEnumVer2.CORPORATE.toString(), label: 'Corporate' },
  // { value: ProjectCampaignEnumVer2.CRISIS_MANAGEMENT.toString(), label: 'Crisis Management' },
  // { value: ProjectCampaignEnumVer2.EVENT.toString(), label: 'Event' },
  // { value: ProjectCampaignEnumVer2.FACTORY_OPENING.toString(), label: 'Factory Opening' },
  // { value: ProjectCampaignEnumVer2.GR_ADVOCACY.toString(), label: 'GR-Advocacy' },
  // { value: ProjectCampaignEnumVer2.MARKET_RESEARCH.toString(), label: t('campaign_options.market_research') },
  // { value: ProjectCampaignEnumVer2.MEDIA_RELATION_PR.toString(), label: 'Media Relations' },
  // { value: ProjectCampaignEnumVer2.MI_BRAND_BRANDING.toString(), label: t('campaign_options.mi_brand_branding') },
  // { value: ProjectCampaignEnumVer2.SOCIAL_DIGITAL_CORPORATE.toString(), label: t('campaign_options.social_digital_corporate') },
  // { value: ProjectCampaignEnumVer2.SOCIAL_DIGITAL_CORPORATE.toString(), label: 'Social and Digital Corporate' },
  // { value: ProjectCampaignEnumVer2.SOCIAL_DIGITAL_PRODUCT.toString(), label: 'Social Media and Digital Product' },
  // { value: ProjectCampaignEnumVer2.TVC_VIDEO_PRODUCTION.toString(), label: 'TCV/Video' },
  { value: ProjectCampaignEnumVer2.BCC.toString(), label: 'BCC' },
  { value: ProjectCampaignEnumVer2.CLDD_VTD.toString(), label: 'Chiến lược dẫn dắt về tư duy' },
  { value: ProjectCampaignEnumVer2.CLND.toString(), label: 'Chiến lược nội dung' },
  { value: ProjectCampaignEnumVer2.TTTR_QT.toString(), label: 'Truyền thông thị trường quốc tế' },
  { value: ProjectCampaignEnumVer2.CTKM.toString(), label: 'Chương trình khuyến mại' },
  { value: ProjectCampaignEnumVer2.CSR.toString(), label: 'CSR' },
  { value: ProjectCampaignEnumVer2.DIGITAL_MARKETING.toString(), label: 'Digital Marketing' },
  { value: ProjectCampaignEnumVer2.GTM_TNTRM.toString(), label: 'GTM Thâm nhập thị trường mới' },
  { value: ProjectCampaignEnumVer2.HALD.toString(), label: 'Hình ảnh lãnh đạo' },
  { value: ProjectCampaignEnumVer2.IMC.toString(), label: 'Influencer Marketing Campaign' },
  { value: ProjectCampaignEnumVer2.KHTR.toString(), label: 'Kích hoạt tài trợ' },
  { value: ProjectCampaignEnumVer2.SNP_MEDIA_RELATIONS.toString(), label: 'SNP Media Relations' },
  { value: ProjectCampaignEnumVer2.PRODUCT_LAUNCH.toString(), label: 'Product Launch' },
  { value: ProjectCampaignEnumVer2.SOCIAL_MEDIA_COMMUNICATION.toString(), label: 'Social Media Communication' },
  { value: ProjectCampaignEnumVer2.TM_LTT.toString(), label: 'Truyền miệng và lòng trung thành' },
  { value: ProjectCampaignEnumVer2.TT_IPO.toString(), label: 'Truyền thông IPO' },

];

export const CAMPAIGN_LIST_BY_TYPE: Record<ProjectTypeVer2Enum, CampaignListType[]> = {
  [ProjectTypeVer2Enum.BLUE_C]: BlueCCampaignList,
  [ProjectTypeVer2Enum.MI_BRAND]: MiBrandCampaignList,
  [ProjectTypeVer2Enum.MVV_ACADEMY]: MVVAcademyCampaignList,
  [ProjectTypeVer2Enum.SNP]: SNPCampaignList,
};
